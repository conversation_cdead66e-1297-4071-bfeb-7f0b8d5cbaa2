const React = require('react');
const { useState, useEffect } = React;

const TopBar = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);

  useEffect(() => {
    // تحديث الوقت كل ثانية
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // جلب الإشعارات
    loadNotifications();

    return () => clearInterval(timer);
  }, []);

  const loadNotifications = async () => {
    try {
      const data = await window.api.notifications.getAll();
      setNotifications(data.filter(n => !n.read).slice(0, 5));
    } catch (error) {
      console.error('خطأ في جلب الإشعارات:', error);
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const markAllAsRead = async () => {
    try {
      await window.api.notifications.markAllAsRead();
      setNotifications([]);
      setShowNotifications(false);
    } catch (error) {
      console.error('خطأ في تحديث الإشعارات:', error);
    }
  };

  return (
    <div className="top-bar">
      <div className="top-bar-content">
        {/* معلومات التطبيق */}
        <div className="app-info">
          <h1 className="app-title">
            <i className="fas fa-hammer icon-gradient"></i>
            اتش قروب
          </h1>
          <p className="app-subtitle">نظام إدارة ورشة النجارة</p>
        </div>

        {/* الوقت والتاريخ */}
        <div className="datetime-display">
          <div className="time-display">
            <i className="fas fa-clock"></i>
            <span className="time">{formatTime(currentTime)}</span>
          </div>
          <div className="date-display">
            <i className="fas fa-calendar"></i>
            <span className="date">{formatDate(currentTime)}</span>
          </div>
        </div>

        {/* الإشعارات */}
        <div className="notifications-section">
          <button 
            className="notifications-btn"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <i className="fas fa-bell"></i>
            {notifications.length > 0 && (
              <span className="notifications-count">{notifications.length}</span>
            )}
          </button>

          {showNotifications && (
            <div className="notifications-dropdown">
              <div className="notifications-header">
                <h5>الإشعارات</h5>
                {notifications.length > 0 && (
                  <button 
                    className="mark-all-read-btn"
                    onClick={markAllAsRead}
                  >
                    تحديد الكل كمقروء
                  </button>
                )}
              </div>
              
              <div className="notifications-list">
                {notifications.length === 0 ? (
                  <div className="no-notifications">
                    <i className="fas fa-check-circle"></i>
                    <p>لا توجد إشعارات جديدة</p>
                  </div>
                ) : (
                  notifications.map((notification, index) => (
                    <div key={index} className="notification-item">
                      <div className="notification-icon">
                        <i className={`fas ${getNotificationIcon(notification.type)}`}></i>
                      </div>
                      <div className="notification-content">
                        <h6 className="notification-title">{notification.title}</h6>
                        <p className="notification-message">{notification.message}</p>
                        <span className="notification-time">
                          {new Date(notification.created_at).toLocaleString('ar-SA')}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>

        {/* إعدادات سريعة */}
        <div className="quick-actions">
          <button className="quick-action-btn" title="إعدادات">
            <i className="fas fa-cog"></i>
          </button>
          <button className="quick-action-btn" title="ملء الشاشة">
            <i className="fas fa-expand"></i>
          </button>
          <button className="quick-action-btn" title="تسجيل الخروج">
            <i className="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

const getNotificationIcon = (type) => {
  switch (type) {
    case 'order': return 'fa-shopping-cart';
    case 'payment': return 'fa-money-bill-wave';
    case 'inventory': return 'fa-boxes';
    case 'worker': return 'fa-user-hard-hat';
    case 'deadline': return 'fa-clock';
    case 'low_inventory': return 'fa-exclamation-triangle';
    default: return 'fa-info-circle';
  }
};

module.exports = TopBar;
