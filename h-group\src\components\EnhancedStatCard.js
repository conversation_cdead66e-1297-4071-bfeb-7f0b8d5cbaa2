const React = require('react');
const { useState, useEffect } = React;

const EnhancedStatCard = ({ 
  title, 
  value, 
  icon, 
  color = 'primary', 
  trend = null, 
  subtitle = null,
  onClick = null,
  loading = false,
  animated = true 
}) => {
  const [displayValue, setDisplayValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // تأثير العد التصاعدي
    if (animated && typeof value === 'number' && value > 0) {
      const duration = 1500; // مدة الرسوم المتحركة بالميلي ثانية
      const steps = 60;
      const increment = value / steps;
      let current = 0;
      
      const timer = setInterval(() => {
        current += increment;
        if (current >= value) {
          setDisplayValue(value);
          clearInterval(timer);
        } else {
          setDisplayValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(timer);
    } else {
      setDisplayValue(value);
    }
  }, [value, animated]);

  useEffect(() => {
    // تأثير الظهور التدريجي
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const getColorClass = (colorType) => {
    const colors = {
      primary: 'stat-card-primary',
      secondary: 'stat-card-secondary',
      success: 'stat-card-success',
      warning: 'stat-card-warning',
      danger: 'stat-card-danger',
      info: 'stat-card-info'
    };
    return colors[colorType] || colors.primary;
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend > 0) {
      return <i className="fas fa-arrow-up trend-up"></i>;
    } else if (trend < 0) {
      return <i className="fas fa-arrow-down trend-down"></i>;
    } else {
      return <i className="fas fa-minus trend-neutral"></i>;
    }
  };

  const formatValue = (val) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return (val / 1000000).toFixed(1) + 'م';
      } else if (val >= 1000) {
        return (val / 1000).toFixed(1) + 'ك';
      }
      return val.toLocaleString('ar-SA');
    }
    return val;
  };

  return (
    <div 
      className={`
        enhanced-stat-card 
        ${getColorClass(color)} 
        ${isVisible ? 'visible' : ''} 
        ${onClick ? 'clickable' : ''}
        ${loading ? 'loading' : ''}
      `}
      onClick={onClick}
    >
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
        </div>
      )}
      
      <div className="stat-card-header">
        <div className="stat-icon-container">
          <i className={`${icon} stat-icon`}></i>
        </div>
        
        {trend !== null && (
          <div className="trend-indicator">
            {getTrendIcon()}
            <span className="trend-value">{Math.abs(trend)}%</span>
          </div>
        )}
      </div>

      <div className="stat-card-body">
        <h3 className="stat-title">{title}</h3>
        <div className="stat-value-container">
          <span className="stat-value">{formatValue(displayValue)}</span>
          {subtitle && <span className="stat-subtitle">{subtitle}</span>}
        </div>
      </div>

      <div className="stat-card-footer">
        <div className="progress-indicator">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ 
                width: animated && isVisible ? '100%' : '0%',
                transitionDelay: '0.5s'
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* تأثير الإضاءة */}
      <div className="glow-effect"></div>
      
      {/* تأثير الموجة */}
      <div className="wave-effect"></div>
    </div>
  );
};

module.exports = EnhancedStatCard;
