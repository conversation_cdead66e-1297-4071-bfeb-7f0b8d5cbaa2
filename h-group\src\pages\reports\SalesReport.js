const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const SalesReport = () => {
  const { hasPermission } = useAuth();

  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('reports_sales')) {
      alert('ليس لديك صلاحية للوصول إلى هذا التقرير');
      navigate('/reports');
    }
  }, [hasPermission]);

  // فلاتر التقرير
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    groupBy: 'month'
  });

  // بيانات التقرير
  const [reportData, setReportData] = useState({
    summary: {
      totalSales: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      highestSale: 0
    },
    salesByPeriod: [],
    topProducts: [],
    topCustomers: []
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // معالجة تغيير الفلاتر
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // توليد التقرير
  const generateReport = async () => {
    try {
      setLoading(true);
      setError(null);

      // التحقق من صحة التواريخ
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);

      if (startDate > endDate) {
        throw new Error('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
      }

      // استدعاء API لجلب بيانات التقرير
      const data = await window.api.reports.getSalesReport(filters);

      // التأكد من وجود البيانات وتعيين القيم الافتراضية
      const safeData = {
        summary: {
          totalSales: data?.summary?.totalSales || 0,
          totalOrders: data?.summary?.totalOrders || 0,
          averageOrderValue: data?.summary?.averageOrderValue || 0,
          highestSale: data?.summary?.highestSale || 0
        },
        salesByPeriod: Array.isArray(data?.salesByPeriod) ? data.salesByPeriod : [],
        topProducts: Array.isArray(data?.topProducts) ? data.topProducts : [],
        topCustomers: Array.isArray(data?.topCustomers) ? data.topCustomers : []
      };

      setReportData(safeData);
    } catch (error) {
      console.error('خطأ في توليد التقرير:', error);
      setError(error.message || 'حدث خطأ أثناء توليد التقرير. يرجى المحاولة مرة أخرى.');

      // تعيين بيانات فارغة في حالة الخطأ
      setReportData({
        summary: {
          totalSales: 0,
          totalOrders: 0,
          averageOrderValue: 0,
          highestSale: 0
        },
        salesByPeriod: [],
        topProducts: [],
        topCustomers: []
      });
    } finally {
      setLoading(false);
    }
  };

  // توليد التقرير عند تحميل الصفحة
  useEffect(() => {
    generateReport();
  }, []);

  // تصدير التقرير إلى Excel
  const exportToExcel = async () => {
    try {
      const result = await window.api.reports.exportSalesReport(filters);

      if (result.success) {
        alert(`تم تصدير التقرير بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير التقرير');
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.');
    }
  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  return (
    <div className="sales-report-page">
      <div className="page-header">
        <h2>تقرير المبيعات</h2>
        <div className="page-actions">
          <button className="btn btn-success" onClick={exportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>

          <button className="btn btn-info" onClick={printReport}>
            <i className="fas fa-print"></i> طباعة
          </button>

          <Link to="/reports" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للتقارير
          </Link>
        </div>
      </div>

      {/* فلاتر التقرير */}
      <div className="card mb-4">
        <div className="card-header">فلاتر التقرير</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  name="startDate"
                  value={filters.startDate}
                  onChange={handleFilterChange}
                />
              </div>
            </div>

            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  name="endDate"
                  value={filters.endDate}
                  onChange={handleFilterChange}
                />
              </div>
            </div>

            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">تجميع حسب</label>
                <select
                  className="form-control"
                  name="groupBy"
                  value={filters.groupBy}
                  onChange={handleFilterChange}
                >
                  <option value="day">يوم</option>
                  <option value="week">أسبوع</option>
                  <option value="month">شهر</option>
                  <option value="quarter">ربع سنة</option>
                  <option value="year">سنة</option>
                </select>
              </div>
            </div>
          </div>

          <div className="filter-actions">
            <button
              className="btn btn-primary"
              onClick={generateReport}
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                  <span className="ms-1">جاري التحميل...</span>
                </>
              ) : (
                <>
                  <i className="fas fa-sync"></i> توليد التقرير
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      {/* ملخص المبيعات */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-content">
              <h3>إجمالي المبيعات</h3>
              <div className="stat-value">{(reportData?.summary?.totalSales || 0).toLocaleString()} ر.س</div>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-content">
              <h3>عدد الطلبات</h3>
              <div className="stat-value">{reportData?.summary?.totalOrders || 0}</div>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="stat-content">
              <h3>متوسط قيمة الطلب</h3>
              <div className="stat-value">{(reportData?.summary?.averageOrderValue || 0).toLocaleString()} ر.س</div>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-trophy"></i>
            </div>
            <div className="stat-content">
              <h3>أعلى قيمة طلب</h3>
              <div className="stat-value">{(reportData?.summary?.highestSale || 0).toLocaleString()} ر.س</div>
            </div>
          </div>
        </div>
      </div>

      {/* المبيعات حسب الفترة */}
      <div className="card mb-4">
        <div className="card-header">
          <h5>المبيعات حسب الفترة</h5>
        </div>
        <div className="card-body">
          {!reportData?.salesByPeriod || reportData.salesByPeriod.length === 0 ? (
            <div className="alert alert-info">لا توجد بيانات للعرض</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>الفترة</th>
                    <th>عدد الطلبات</th>
                    <th>إجمالي المبيعات</th>
                    <th>متوسط قيمة الطلب</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.salesByPeriod.map((period, index) => (
                    <tr key={index}>
                      <td>{period?.period || 'غير محدد'}</td>
                      <td>{period?.orderCount || 0}</td>
                      <td>{(period?.totalSales || 0).toLocaleString()} ر.س</td>
                      <td>{(period?.averageOrderValue || 0).toLocaleString()} ر.س</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <div className="row">
        {/* أفضل المنتجات مبيعًا */}
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h5>أفضل المنتجات مبيعًا</h5>
            </div>
            <div className="card-body">
              {!reportData?.topProducts || reportData.topProducts.length === 0 ? (
                <div className="alert alert-info">لا توجد بيانات للعرض</div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead>
                      <tr>
                        <th>المنتج</th>
                        <th>عدد الطلبات</th>
                        <th>إجمالي المبيعات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.topProducts.map((product, index) => (
                        <tr key={index}>
                          <td>{product?.productName || 'غير محدد'}</td>
                          <td>{product?.orderCount || 0}</td>
                          <td>{(product?.totalSales || 0).toLocaleString()} ر.س</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* أفضل العملاء */}
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h5>أفضل العملاء</h5>
            </div>
            <div className="card-body">
              {!reportData?.topCustomers || reportData.topCustomers.length === 0 ? (
                <div className="alert alert-info">لا توجد بيانات للعرض</div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead>
                      <tr>
                        <th>العميل</th>
                        <th>عدد الطلبات</th>
                        <th>إجمالي المشتريات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.topCustomers.map((customer, index) => (
                        <tr key={index}>
                          <td>{customer?.customerName || 'غير محدد'}</td>
                          <td>{customer?.orderCount || 0}</td>
                          <td>{(customer?.totalSpent || 0).toLocaleString()} ر.س</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = SalesReport;
